#!/usr/bin/env python
"""
初始化数据脚本
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend_django.settings')
django.setup()

from authentication.models import User, Role, Permission, UserRole, RolePermission
from menu_management.models import Menu
from system_management.models import Department, Position, UserDepartment


def create_roles_and_permissions():
    """创建角色和权限"""
    print("创建角色和权限...")
    
    # 创建权限
    permissions_data = [
        {'name': '用户管理', 'code': 'AC_100100', 'resource': 'user', 'action': 'manage'},
        {'name': '用户查询', 'code': 'AC_100110', 'resource': 'user', 'action': 'view'},
        {'name': '用户新增', 'code': 'AC_100120', 'resource': 'user', 'action': 'create'},
        {'name': '用户编辑', 'code': 'AC_100130', 'resource': 'user', 'action': 'edit'},
        {'name': '用户删除', 'code': 'AC_100140', 'resource': 'user', 'action': 'delete'},
        {'name': '角色管理', 'code': 'AC_100200', 'resource': 'role', 'action': 'manage'},
        {'name': '菜单管理', 'code': 'AC_100300', 'resource': 'menu', 'action': 'manage'},
        {'name': '系统管理', 'code': 'AC_100010', 'resource': 'system', 'action': 'manage'},
        {'name': '部门管理', 'code': 'AC_100020', 'resource': 'department', 'action': 'manage'},
        {'name': '文件上传', 'code': 'AC_100030', 'resource': 'file', 'action': 'upload'},
    ]
    
    for perm_data in permissions_data:
        permission, created = Permission.objects.get_or_create(
            code=perm_data['code'],
            defaults=perm_data
        )
        if created:
            print(f"  创建权限: {permission.name}")
    
    # 创建角色
    roles_data = [
        {'name': '超级管理员', 'code': 'super', 'description': '拥有所有权限'},
        {'name': '管理员', 'code': 'admin', 'description': '拥有大部分管理权限'},
        {'name': '普通用户', 'code': 'user', 'description': '基本用户权限'},
    ]
    
    for role_data in roles_data:
        role, created = Role.objects.get_or_create(
            code=role_data['code'],
            defaults=role_data
        )
        if created:
            print(f"  创建角色: {role.name}")
    
    # 分配权限给角色
    super_role = Role.objects.get(code='super')
    admin_role = Role.objects.get(code='admin')
    user_role = Role.objects.get(code='user')
    
    # 超级管理员拥有所有权限
    for permission in Permission.objects.all():
        RolePermission.objects.get_or_create(role=super_role, permission=permission)
    
    # 管理员拥有部分权限
    admin_permissions = ['AC_100010', 'AC_100020', 'AC_100030']
    for code in admin_permissions:
        permission = Permission.objects.get(code=code)
        RolePermission.objects.get_or_create(role=admin_role, permission=permission)
    
    # 普通用户权限
    user_permissions = ['AC_100030']  # 只能上传文件
    for code in user_permissions:
        permission = Permission.objects.get(code=code)
        RolePermission.objects.get_or_create(role=user_role, permission=permission)


def create_users():
    """创建测试用户"""
    print("创建测试用户...")
    
    users_data = [
        {
            'username': 'vben',
            'email': '<EMAIL>',
            'password': '123456',
            'real_name': 'Vben',
            'home_path': '/analytics',
            'role': 'super'
        },
        {
            'username': 'jack',
            'email': '<EMAIL>',
            'password': '123456',
            'real_name': 'Jack',
            'home_path': '/analytics',
            'role': 'user'
        }
    ]
    
    for user_data in users_data:
        role_code = user_data.pop('role')
        user, created = User.objects.get_or_create(
            username=user_data['username'],
            defaults=user_data
        )
        if created:
            user.set_password(user_data['password'])
            user.save()
            print(f"  创建用户: {user.username}")
            
            # 分配角色
            role = Role.objects.get(code=role_code)
            UserRole.objects.get_or_create(user=user, role=role)


def create_departments():
    """创建部门"""
    print("创建部门...")
    
    departments_data = [
        {'name': '技术部', 'code': 'TECH', 'description': '负责技术开发和维护'},
        {'name': '产品部', 'code': 'PRODUCT', 'description': '负责产品设计和规划'},
        {'name': '运营部', 'code': 'OPERATION', 'description': '负责运营和推广'},
    ]
    
    for dept_data in departments_data:
        department, created = Department.objects.get_or_create(
            code=dept_data['code'],
            defaults=dept_data
        )
        if created:
            print(f"  创建部门: {department.name}")


def create_menus():
    """创建菜单"""
    print("创建菜单...")
    
    # 创建主菜单
    dashboard_menu, created = Menu.objects.get_or_create(
        name='Dashboard',
        defaults={
            'path': '/dashboard',
            'type': 'catalog',
            'title': 'Dashboard',
            'icon': 'carbon:dashboard',
            'order': -1,
        }
    )
    if created:
        print(f"  创建菜单: {dashboard_menu.title}")
    
    # 创建子菜单
    analytics_menu, created = Menu.objects.get_or_create(
        name='Analytics',
        defaults={
            'path': '/analytics',
            'component': '/dashboard/analytics/index',
            'type': 'menu',
            'title': 'Analytics',
            'parent': dashboard_menu,
            'affix_tab': True,
            'order': 1,
        }
    )
    if created:
        print(f"  创建菜单: {analytics_menu.title}")
    
    workspace_menu, created = Menu.objects.get_or_create(
        name='Workspace',
        defaults={
            'path': '/workspace',
            'component': '/dashboard/workspace/index',
            'type': 'menu',
            'title': 'Workspace',
            'parent': dashboard_menu,
            'order': 2,
        }
    )
    if created:
        print(f"  创建菜单: {workspace_menu.title}")
    
    # 系统管理菜单
    system_menu, created = Menu.objects.get_or_create(
        name='System',
        defaults={
            'path': '/system',
            'type': 'catalog',
            'title': 'System Management',
            'icon': 'carbon:settings',
            'order': 9997,
        }
    )
    if created:
        print(f"  创建菜单: {system_menu.title}")


def main():
    """主函数"""
    print("开始初始化数据...")
    
    create_roles_and_permissions()
    create_users()
    create_departments()
    create_menus()
    
    print("数据初始化完成！")


if __name__ == '__main__':
    main()
