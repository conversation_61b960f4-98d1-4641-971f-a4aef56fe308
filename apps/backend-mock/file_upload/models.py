from django.db import models
import os


def upload_to(instance, filename):
    """文件上传路径"""
    # 按日期分目录存储
    from datetime import datetime
    date_path = datetime.now().strftime('%Y/%m/%d')
    return f'uploads/{date_path}/{filename}'


class UploadedFile(models.Model):
    """上传文件模型"""
    FILE_TYPE_CHOICES = [
        ('image', '图片'),
        ('document', '文档'),
        ('video', '视频'),
        ('audio', '音频'),
        ('other', '其他'),
    ]

    name = models.CharField(max_length=255, verbose_name="文件名")
    original_name = models.CharField(max_length=255, verbose_name="原始文件名")
    file = models.FileField(upload_to=upload_to, verbose_name="文件")
    file_type = models.CharField(max_length=20, choices=FILE_TYPE_CHOICES, default='other', verbose_name="文件类型")
    file_size = models.BigIntegerField(verbose_name="文件大小(字节)")
    mime_type = models.CharField(max_length=100, verbose_name="MIME类型")
    md5_hash = models.CharField(max_length=32, verbose_name="MD5哈希", blank=True)
    uploader = models.ForeignKey('authentication.User', on_delete=models.CASCADE, verbose_name="上传者")
    upload_ip = models.GenericIPAddressField(verbose_name="上传IP", blank=True, null=True)
    is_public = models.BooleanField(default=False, verbose_name="是否公开")
    download_count = models.IntegerField(default=0, verbose_name="下载次数")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="上传时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "上传文件"
        verbose_name_plural = "上传文件"
        db_table = "file_upload"
        ordering = ['-created_at']

    def __str__(self):
        return self.original_name

    @property
    def file_url(self):
        """获取文件URL"""
        if self.file:
            return self.file.url
        return None

    @property
    def file_extension(self):
        """获取文件扩展名"""
        return os.path.splitext(self.original_name)[1].lower()

    def get_file_type_from_extension(self):
        """根据扩展名判断文件类型"""
        ext = self.file_extension
        if ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
            return 'image'
        elif ext in ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt']:
            return 'document'
        elif ext in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']:
            return 'video'
        elif ext in ['.mp3', '.wav', '.flac', '.aac', '.ogg']:
            return 'audio'
        else:
            return 'other'

    def save(self, *args, **kwargs):
        """保存时自动设置文件类型"""
        if not self.file_type or self.file_type == 'other':
            self.file_type = self.get_file_type_from_extension()
        super().save(*args, **kwargs)
