# Generated by Django 5.2.1 on 2025-06-04 05:09

import django.db.models.deletion
import file_upload.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UploadedFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='文件名')),
                ('original_name', models.CharField(max_length=255, verbose_name='原始文件名')),
                ('file', models.FileField(upload_to=file_upload.models.upload_to, verbose_name='文件')),
                ('file_type', models.CharField(choices=[('image', '图片'), ('document', '文档'), ('video', '视频'), ('audio', '音频'), ('other', '其他')], default='other', max_length=20, verbose_name='文件类型')),
                ('file_size', models.BigIntegerField(verbose_name='文件大小(字节)')),
                ('mime_type', models.CharField(max_length=100, verbose_name='MIME类型')),
                ('md5_hash', models.CharField(blank=True, max_length=32, verbose_name='MD5哈希')),
                ('upload_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='上传IP')),
                ('is_public', models.BooleanField(default=False, verbose_name='是否公开')),
                ('download_count', models.IntegerField(default=0, verbose_name='下载次数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('uploader', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='上传者')),
            ],
            options={
                'verbose_name': '上传文件',
                'verbose_name_plural': '上传文件',
                'db_table': 'file_upload',
                'ordering': ['-created_at'],
            },
        ),
    ]
