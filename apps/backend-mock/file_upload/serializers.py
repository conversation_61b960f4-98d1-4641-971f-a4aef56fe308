from rest_framework import serializers
from .models import UploadedFile
import hashlib


class UploadedFileSerializer(serializers.ModelSerializer):
    """上传文件序列化器"""
    uploader_name = serializers.CharField(source='uploader.real_name', read_only=True)
    file_url = serializers.ReadOnlyField()
    file_extension = serializers.ReadOnlyField()
    
    class Meta:
        model = UploadedFile
        fields = ['id', 'name', 'original_name', 'file', 'file_url', 'file_type',
                 'file_size', 'file_extension', 'mime_type', 'md5_hash', 'uploader',
                 'uploader_name', 'upload_ip', 'is_public', 'download_count',
                 'created_at', 'updated_at']
        read_only_fields = ['id', 'file_size', 'mime_type', 'md5_hash', 'uploader',
                           'upload_ip', 'download_count', 'created_at', 'updated_at']


class FileUploadSerializer(serializers.ModelSerializer):
    """文件上传序列化器"""
    file = serializers.FileField()
    
    class Meta:
        model = UploadedFile
        fields = ['file', 'name', 'is_public']
    
    def create(self, validated_data):
        file = validated_data['file']
        request = self.context['request']
        
        # 设置文件信息
        validated_data['original_name'] = file.name
        validated_data['file_size'] = file.size
        validated_data['mime_type'] = file.content_type or 'application/octet-stream'
        validated_data['uploader'] = request.user
        validated_data['upload_ip'] = self.get_client_ip(request)
        
        # 如果没有指定名称，使用原始文件名
        if not validated_data.get('name'):
            validated_data['name'] = file.name
        
        # 计算MD5哈希
        validated_data['md5_hash'] = self.calculate_md5(file)
        
        return super().create(validated_data)
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def calculate_md5(self, file):
        """计算文件MD5哈希"""
        hash_md5 = hashlib.md5()
        for chunk in file.chunks():
            hash_md5.update(chunk)
        return hash_md5.hexdigest()


class FileListSerializer(serializers.ModelSerializer):
    """文件列表序列化器（简化版）"""
    uploader_name = serializers.CharField(source='uploader.real_name', read_only=True)
    file_url = serializers.ReadOnlyField()
    file_size_display = serializers.SerializerMethodField()
    
    class Meta:
        model = UploadedFile
        fields = ['id', 'name', 'original_name', 'file_url', 'file_type',
                 'file_size', 'file_size_display', 'uploader_name', 'is_public',
                 'download_count', 'created_at']
    
    def get_file_size_display(self, obj):
        """格式化文件大小显示"""
        size = obj.file_size
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"
