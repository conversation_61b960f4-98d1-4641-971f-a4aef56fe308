# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2019.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-02-22 17:30+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language: pl_PL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr "Nagłówek autoryzacji musi zawierać dwie wartości rodzielone spacjami"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "Podany token jest błędny dla każdego typu tokena"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr "Token nie zawierał rozpoznawalnej identyfikacji użytkownika"

#: authentication.py:132
msgid "User not found"
msgstr "Użytkownik nie znaleziony"

#: authentication.py:135
msgid "User is inactive"
msgstr "Użytkownik jest nieaktywny"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "Nierozpoznany typ algorytmu '{}'"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr ""

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""

#: backends.py:125 backends.py:177 tokens.py:68
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "Token jest niepoprawny lub wygasł"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr ""

#: backends.py:175 tokens.py:66
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "Token jest niepoprawny lub wygasł"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "Token jest niepoprawny lub wygasł"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Nie znaleziono aktywnego konta dla podanych danych uwierzytelniających"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "Nie znaleziono aktywnego konta dla podanych danych uwierzytelniających"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"Ustawienie '{}' zostało usunięte. Dostępne ustawienia znajdują sie w '{}'"

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "użytkownik"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "stworzony w"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "wygasa o"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Token Blacklist"

#: tokens.py:52
msgid "Cannot create token with no type or lifetime"
msgstr "Nie można utworzyć tokena bez podanego typu lub żywotności"

#: tokens.py:126
msgid "Token has no id"
msgstr "Token nie posiada numeru identyfikacyjnego"

#: tokens.py:138
msgid "Token has no type"
msgstr "Token nie posiada typu"

#: tokens.py:141
msgid "Token has wrong type"
msgstr "Token posiada zły typ"

#: tokens.py:200
msgid "Token has no '{}' claim"
msgstr "Token nie posiada upoważnienia '{}'"

#: tokens.py:205
msgid "Token '{}' claim has expired"
msgstr "Upoważnienie tokena '{}' wygasło"

#: tokens.py:292
msgid "Token is blacklisted"
msgstr "Token znajduję się na czarnej liście"
