from rest_framework import status, generics, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import get_user_model
from .models import User, Role, Permission, UserRole, RolePermission
from .serializers import (
    UserSerializer, UserCreateSerializer, RoleSerializer, PermissionSerializer,
    LoginSerializer, ChangePasswordSerializer
)

User = get_user_model()


class LogoutView(APIView):
    permission_classes = []  # 允许无认证访问，因为token可能已过期

    def post(self, request):
        try:
            # 尝试从Authorization header获取token
            auth_header = request.META.get('HTTP_AUTHORIZATION', '')
            if auth_header.startswith('Bearer '):
                access_token = auth_header[7:]  # 移除 'Bearer ' 前缀
                try:
                    # 验证并获取token信息（不要求token有效）
                    from rest_framework_simplejwt.tokens import UntypedToken
                    UntypedToken(access_token)
                except Exception:
                    # Token无效或过期，忽略
                    pass

            # 尝试处理refresh token
            refresh_token = request.data.get("refresh")
            if refresh_token:
                try:
                    token = RefreshToken(refresh_token)
                    token.blacklist()
                except Exception:
                    # 如果blacklist失败，忽略错误
                    pass

            # 总是返回成功，因为登出主要是清除客户端token
            return Response({"message": "Successfully logged out"}, status=status.HTTP_200_OK)
        except Exception as e:
            # 即使出错也返回成功，确保前端能正常登出
            return Response({"message": "Successfully logged out"}, status=status.HTTP_200_OK)


class UserInfoView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        serializer = UserSerializer(user)
        return Response(serializer.data)


class AccessCodesView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        # 获取用户的所有权限代码
        user_roles = UserRole.objects.filter(user=user)
        permission_codes = []

        for user_role in user_roles:
            role_permissions = RolePermission.objects.filter(role=user_role.role)
            for rp in role_permissions:
                if rp.permission.code not in permission_codes:
                    permission_codes.append(rp.permission.code)

        return Response(permission_codes)
