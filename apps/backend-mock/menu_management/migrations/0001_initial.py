# Generated by Django 5.2.1 on 2025-06-04 05:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Menu',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='菜单名称')),
                ('path', models.CharField(blank=True, max_length=200, verbose_name='路由路径')),
                ('component', models.CharField(blank=True, max_length=200, verbose_name='组件路径')),
                ('type', models.CharField(choices=[('catalog', '目录'), ('menu', '菜单'), ('button', '按钮'), ('embedded', '内嵌'), ('link', '外链')], default='menu', max_length=20, verbose_name='菜单类型')),
                ('status', models.IntegerField(choices=[(0, '禁用'), (1, '启用')], default=1, verbose_name='状态')),
                ('order', models.IntegerField(default=0, verbose_name='排序')),
                ('auth_code', models.CharField(blank=True, max_length=100, verbose_name='权限代码')),
                ('icon', models.CharField(blank=True, max_length=100, verbose_name='图标')),
                ('title', models.CharField(max_length=100, verbose_name='标题')),
                ('affix_tab', models.BooleanField(default=False, verbose_name='固定标签页')),
                ('keep_alive', models.BooleanField(default=False, verbose_name='缓存页面')),
                ('badge', models.CharField(blank=True, max_length=50, verbose_name='徽章')),
                ('badge_type', models.CharField(blank=True, max_length=20, verbose_name='徽章类型')),
                ('badge_variants', models.CharField(blank=True, max_length=20, verbose_name='徽章变体')),
                ('link', models.URLField(blank=True, verbose_name='外链地址')),
                ('iframe_src', models.URLField(blank=True, verbose_name='内嵌地址')),
                ('menu_visible_with_forbidden', models.BooleanField(default=False, verbose_name='禁止时菜单可见')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='menu_management.menu', verbose_name='父菜单')),
            ],
            options={
                'verbose_name': '菜单',
                'verbose_name_plural': '菜单',
                'db_table': 'menu',
                'ordering': ['order', 'id'],
            },
        ),
    ]
