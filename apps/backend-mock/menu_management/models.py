from django.db import models


class Menu(models.Model):
    """菜单模型"""
    MENU_TYPE_CHOICES = [
        ('catalog', '目录'),
        ('menu', '菜单'),
        ('button', '按钮'),
        ('embedded', '内嵌'),
        ('link', '外链'),
    ]

    STATUS_CHOICES = [
        (0, '禁用'),
        (1, '启用'),
    ]

    name = models.CharField(max_length=100, verbose_name="菜单名称")
    path = models.CharField(max_length=200, verbose_name="路由路径", blank=True)
    component = models.CharField(max_length=200, verbose_name="组件路径", blank=True)
    type = models.CharField(max_length=20, choices=MENU_TYPE_CHOICES, default='menu', verbose_name="菜单类型")
    status = models.IntegerField(choices=STATUS_CHOICES, default=1, verbose_name="状态")
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name="父菜单")
    order = models.IntegerField(default=0, verbose_name="排序")
    auth_code = models.CharField(max_length=100, verbose_name="权限代码", blank=True)

    # Meta信息
    icon = models.CharField(max_length=100, verbose_name="图标", blank=True)
    title = models.CharField(max_length=100, verbose_name="标题")
    affix_tab = models.BooleanField(default=False, verbose_name="固定标签页")
    keep_alive = models.BooleanField(default=False, verbose_name="缓存页面")
    badge = models.CharField(max_length=50, verbose_name="徽章", blank=True)
    badge_type = models.CharField(max_length=20, verbose_name="徽章类型", blank=True)
    badge_variants = models.CharField(max_length=20, verbose_name="徽章变体", blank=True)
    link = models.URLField(verbose_name="外链地址", blank=True)
    iframe_src = models.URLField(verbose_name="内嵌地址", blank=True)
    menu_visible_with_forbidden = models.BooleanField(default=False, verbose_name="禁止时菜单可见")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "菜单"
        verbose_name_plural = "菜单"
        db_table = "menu"
        ordering = ['order', 'id']

    def __str__(self):
        return self.title

    @property
    def children_count(self):
        """子菜单数量"""
        return self.menu_set.filter(status=1).count()

    def get_children(self):
        """获取子菜单"""
        return self.menu_set.filter(status=1).order_by('order', 'id')

    def get_all_children(self):
        """递归获取所有子菜单"""
        children = []
        for child in self.get_children():
            children.append(child)
            children.extend(child.get_all_children())
        return children
