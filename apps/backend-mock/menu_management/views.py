from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .models import Menu
from .serializers import (
    MenuSerializer, MenuTreeSerializer, MenuCreateUpdateSerializer, MenuListSerializer
)


class MenuViewSet(viewsets.ModelViewSet):
    """菜单管理ViewSet"""
    queryset = Menu.objects.all()
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return MenuCreateUpdateSerializer
        elif self.action == 'list':
            return MenuListSerializer
        return MenuSerializer

    def get_queryset(self):
        queryset = Menu.objects.all()

        # 支持按类型过滤
        menu_type = self.request.query_params.get('type')
        if menu_type:
            queryset = queryset.filter(type=menu_type)

        # 支持按状态过滤
        status_param = self.request.query_params.get('status')
        if status_param is not None:
            queryset = queryset.filter(status=status_param)

        # 支持按父菜单过滤
        parent_id = self.request.query_params.get('parent')
        if parent_id:
            queryset = queryset.filter(parent_id=parent_id)
        elif parent_id == '0':  # 获取根菜单
            queryset = queryset.filter(parent__isnull=True)

        return queryset.order_by('order', 'id')

    @action(detail=False, methods=['get'])
    def tree(self, request):
        """获取菜单树"""
        # 只获取根菜单（没有父菜单的菜单）
        root_menus = Menu.objects.filter(parent__isnull=True, status=1).order_by('order', 'id')
        serializer = MenuTreeSerializer(root_menus, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def user_menus(self, request):
        """获取当前用户的菜单"""
        # 这里可以根据用户权限过滤菜单
        # 暂时返回所有启用的菜单
        root_menus = Menu.objects.filter(parent__isnull=True, status=1).order_by('order', 'id')
        serializer = MenuSerializer(root_menus, many=True, context={'request': request})
        return Response(serializer.data)



