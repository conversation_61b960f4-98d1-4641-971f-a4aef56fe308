from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .models import Menu
from .serializers import (
    MenuSerializer, MenuTreeSerializer, MenuCreateUpdateSerializer, MenuListSerializer
)


class MenuViewSet(viewsets.ModelViewSet):
    """菜单管理ViewSet"""
    queryset = Menu.objects.all()
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return MenuCreateUpdateSerializer
        elif self.action == 'list':
            return MenuListSerializer
        return MenuSerializer

    def get_queryset(self):
        queryset = Menu.objects.all()

        # 支持按类型过滤
        menu_type = self.request.query_params.get('type')
        if menu_type:
            queryset = queryset.filter(type=menu_type)

        # 支持按状态过滤
        status_param = self.request.query_params.get('status')
        if status_param is not None:
            queryset = queryset.filter(status=status_param)

        # 支持按父菜单过滤
        parent_id = self.request.query_params.get('parent')
        if parent_id:
            queryset = queryset.filter(parent_id=parent_id)
        elif parent_id == '0':  # 获取根菜单
            queryset = queryset.filter(parent__isnull=True)

        return queryset.order_by('order', 'id')

    @action(detail=False, methods=['get'])
    def tree(self, request):
        """获取菜单树"""
        # 只获取根菜单（没有父菜单的菜单）
        root_menus = Menu.objects.filter(parent__isnull=True, status=1).order_by('order', 'id')
        serializer = MenuTreeSerializer(root_menus, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def user_menus(self, request):
        """获取当前用户的菜单"""
        from authentication.models import UserRole, RolePermission

        user = request.user

        # 获取用户的所有权限代码
        user_roles = UserRole.objects.filter(user=user)
        user_permission_codes = set()

        for user_role in user_roles:
            role_permissions = RolePermission.objects.filter(role=user_role.role)
            for rp in role_permissions:
                user_permission_codes.add(rp.permission.code)

        # 获取所有启用的根菜单
        root_menus = Menu.objects.filter(parent__isnull=True, status=1).order_by('order', 'id')

        # 过滤用户有权限访问的菜单
        accessible_menus = []
        for menu in root_menus:
            if self._has_menu_permission(menu, user_permission_codes):
                accessible_menus.append(menu)

        serializer = MenuSerializer(accessible_menus, many=True, context={'request': request})
        return Response(serializer.data)

    def _has_menu_permission(self, menu, user_permission_codes):
        """检查用户是否有菜单权限"""
        # 如果菜单没有设置权限代码，则默认可访问
        if not menu.auth_code:
            return True

        # 检查用户是否有该菜单的权限代码
        if menu.auth_code in user_permission_codes:
            return True

        # 检查子菜单是否有权限（如果子菜单有权限，父菜单也应该显示）
        children = menu.get_children()
        for child in children:
            if self._has_menu_permission(child, user_permission_codes):
                return True

        return False



