from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'menus', views.MenuViewSet)

urlpatterns = [
    path('', include(router.urls)),
    # 向后兼容的URL
    path('list/', views.MenuViewSet.as_view({'get': 'list'}), name='menu_list'),
    path('detail/<int:pk>/', views.MenuViewSet.as_view({'get': 'retrieve'}), name='menu_detail'),
]
