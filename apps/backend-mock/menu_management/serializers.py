from rest_framework import serializers
from .models import Menu


class MenuSerializer(serializers.ModelSerializer):
    """菜单序列化器"""
    children = serializers.SerializerMethodField()
    meta = serializers.SerializerMethodField()
    
    class Meta:
        model = Menu
        fields = ['id', 'name', 'path', 'component', 'type', 'status', 'parent',
                 'order', 'auth_code', 'meta', 'children', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_children(self, obj):
        """获取子菜单"""
        if self.context.get('include_children', True):
            children = obj.get_children()
            # 避免无限递归，设置不包含子菜单
            context = self.context.copy()
            context['include_children'] = False
            return MenuSerializer(children, many=True, context=context).data
        return []
    
    def get_meta(self, obj):
        """获取meta信息"""
        meta = {
            'title': obj.title,
            'order': obj.order,
        }
        
        if obj.icon:
            meta['icon'] = obj.icon
        if obj.affix_tab:
            meta['affixTab'] = obj.affix_tab
        if obj.keep_alive:
            meta['keepAlive'] = obj.keep_alive
        if obj.badge:
            meta['badge'] = obj.badge
        if obj.badge_type:
            meta['badgeType'] = obj.badge_type
        if obj.badge_variants:
            meta['badgeVariants'] = obj.badge_variants
        if obj.link:
            meta['link'] = obj.link
        if obj.iframe_src:
            meta['iframeSrc'] = obj.iframe_src
        if obj.menu_visible_with_forbidden:
            meta['menuVisibleWithForbidden'] = obj.menu_visible_with_forbidden
        if obj.auth_code:
            meta['authority'] = [obj.auth_code]
        
        return meta


class MenuTreeSerializer(serializers.ModelSerializer):
    """菜单树形序列化器"""
    children = serializers.SerializerMethodField()
    
    class Meta:
        model = Menu
        fields = ['id', 'name', 'title', 'type', 'status', 'parent', 'order', 'children']
    
    def get_children(self, obj):
        """递归获取子菜单"""
        children = obj.get_children()
        return MenuTreeSerializer(children, many=True).data


class MenuCreateUpdateSerializer(serializers.ModelSerializer):
    """菜单创建/更新序列化器"""
    
    class Meta:
        model = Menu
        fields = ['name', 'path', 'component', 'type', 'status', 'parent',
                 'order', 'auth_code', 'icon', 'title', 'affix_tab', 'keep_alive',
                 'badge', 'badge_type', 'badge_variants', 'link', 'iframe_src',
                 'menu_visible_with_forbidden']
    
    def validate_parent(self, value):
        """验证父菜单"""
        if value and self.instance:
            # 检查是否会形成循环引用
            if value.id == self.instance.id:
                raise serializers.ValidationError('不能将自己设为父菜单')
            
            # 检查是否是自己的子菜单
            all_children = self.instance.get_all_children()
            if value in all_children:
                raise serializers.ValidationError('不能将子菜单设为父菜单')
        
        return value


class MenuListSerializer(serializers.ModelSerializer):
    """菜单列表序列化器（简化版）"""
    parent_name = serializers.CharField(source='parent.title', read_only=True)
    children_count = serializers.ReadOnlyField()
    
    class Meta:
        model = Menu
        fields = ['id', 'name', 'title', 'type', 'status', 'parent', 'parent_name',
                 'order', 'auth_code', 'children_count', 'created_at', 'updated_at']
