from django.urls import path
from . import views

urlpatterns = [
    # 部门管理
    path('department/', views.DepartmentListView.as_view(), name='department_list'),
    path('department/list', views.DepartmentListView.as_view(), name='department_list_no_slash'),
    path('dept/', views.DepartmentListView.as_view(), name='dept_list'),  # 前端使用dept简写
    path('dept/list', views.DepartmentListView.as_view(), name='dept_list_no_slash'),

    # 角色管理
    path('role/', views.RoleListView.as_view(), name='role_list'),
    path('role/list', views.RoleListView.as_view(), name='role_list_no_slash'),
    path('role/<int:pk>/', views.RoleDetailView.as_view(), name='role_detail'),
    path('role/code-exists', views.RoleCodeExistsView.as_view(), name='role_code_exists'),

    # 权限管理
    path('permission/', views.PermissionListView.as_view(), name='permission_list'),
    path('permission/list', views.PermissionListView.as_view(), name='permission_list_no_slash'),

    # 用户管理
    path('user/', views.UserListView.as_view(), name='user_list'),
    path('user/list', views.UserListView.as_view(), name='user_list_no_slash'),

    # 菜单管理
    path('menu/', views.MenuManagementListView.as_view(), name='menu_mgmt_list'),
    path('menu/list', views.MenuManagementListView.as_view(), name='menu_mgmt_list_no_slash'),
    path('menu/name-exists', views.MenuNameExistsView.as_view(), name='menu_name_exists'),
    path('menu/path-exists', views.MenuPathExistsView.as_view(), name='menu_path_exists'),
]
