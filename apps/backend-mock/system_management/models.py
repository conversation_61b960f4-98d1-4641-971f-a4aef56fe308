from django.db import models


class Department(models.Model):
    """部门模型"""
    STATUS_CHOICES = [
        (0, '禁用'),
        (1, '启用'),
    ]

    name = models.CharField(max_length=100, verbose_name="部门名称")
    code = models.CharField(max_length=50, unique=True, verbose_name="部门代码")
    description = models.TextField(blank=True, verbose_name="部门描述")
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name="上级部门")
    manager = models.ForeignKey('authentication.User', on_delete=models.SET_NULL, null=True, blank=True, verbose_name="部门负责人")
    order = models.IntegerField(default=0, verbose_name="排序")
    status = models.IntegerField(choices=STATUS_CHOICES, default=1, verbose_name="状态")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "部门"
        verbose_name_plural = "部门"
        db_table = "system_department"
        ordering = ['order', 'id']

    def __str__(self):
        return self.name

    def get_children(self):
        """获取子部门"""
        return self.department_set.filter(status=1).order_by('order', 'id')

    def get_all_children(self):
        """递归获取所有子部门"""
        children = []
        for child in self.get_children():
            children.append(child)
            children.extend(child.get_all_children())
        return children


class Position(models.Model):
    """职位模型"""
    STATUS_CHOICES = [
        (0, '禁用'),
        (1, '启用'),
    ]

    name = models.CharField(max_length=100, verbose_name="职位名称")
    code = models.CharField(max_length=50, unique=True, verbose_name="职位代码")
    description = models.TextField(blank=True, verbose_name="职位描述")
    department = models.ForeignKey(Department, on_delete=models.CASCADE, verbose_name="所属部门")
    level = models.IntegerField(default=1, verbose_name="职位级别")
    status = models.IntegerField(choices=STATUS_CHOICES, default=1, verbose_name="状态")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "职位"
        verbose_name_plural = "职位"
        db_table = "system_position"
        ordering = ['level', 'id']

    def __str__(self):
        return f"{self.department.name} - {self.name}"


class UserDepartment(models.Model):
    """用户部门关联模型"""
    user = models.ForeignKey('authentication.User', on_delete=models.CASCADE, verbose_name="用户")
    department = models.ForeignKey(Department, on_delete=models.CASCADE, verbose_name="部门")
    position = models.ForeignKey(Position, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="职位")
    is_primary = models.BooleanField(default=True, verbose_name="是否主部门")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "用户部门"
        verbose_name_plural = "用户部门"
        db_table = "system_user_department"
        unique_together = ('user', 'department')

    def __str__(self):
        return f"{self.user.username} - {self.department.name}"
