from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth import get_user_model
from django.core.paginator import Paginator
from authentication.models import Role, Permission
from authentication.serializers import RoleSerializer, UserSerializer, PermissionSerializer
from menu_management.models import Menu
from menu_management.serializers import MenuSerializer
from .models import Department
from .serializers import DepartmentSerializer

User = get_user_model()


class DepartmentListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取部门列表"""
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('pageSize', 20))

        departments = Department.objects.filter(status=1).order_by('order', 'id')

        # 分页
        paginator = Paginator(departments, page_size)
        page_obj = paginator.get_page(page)

        serializer = DepartmentSerializer(page_obj.object_list, many=True)

        return Response({
            'list': serializer.data,
            'total': paginator.count,
            'page': page,
            'pageSize': page_size,
            'totalPages': paginator.num_pages,
        })


class RoleListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取角色列表"""
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('pageSize', 20))

        roles = Role.objects.filter(is_active=True).order_by('id')

        # 分页
        paginator = Paginator(roles, page_size)
        page_obj = paginator.get_page(page)

        serializer = RoleSerializer(page_obj.object_list, many=True)

        return Response({
            'list': serializer.data,
            'total': paginator.count,
            'page': page,
            'pageSize': page_size,
            'totalPages': paginator.num_pages,
        })

    def post(self, request):
        """创建角色"""
        serializer = RoleSerializer(data=request.data)
        if serializer.is_valid():
            role = serializer.save()

            # 处理权限分配
            permissions = request.data.get('permissions', [])
            if permissions:
                from authentication.models import RolePermission
                for perm_code in permissions:
                    try:
                        permission = Permission.objects.get(code=perm_code)
                        RolePermission.objects.get_or_create(role=role, permission=permission)
                    except Permission.DoesNotExist:
                        pass

            return Response(RoleSerializer(role).data, status=201)
        return Response(serializer.errors, status=400)


class RoleDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get_object(self, pk):
        try:
            return Role.objects.get(pk=pk)
        except Role.DoesNotExist:
            return None

    def get(self, request, pk):
        """获取角色详情"""
        role = self.get_object(pk)
        if not role:
            return Response({'error': 'Role not found'}, status=404)

        serializer = RoleSerializer(role)
        return Response(serializer.data)

    def put(self, request, pk):
        """更新角色"""
        role = self.get_object(pk)
        if not role:
            return Response({'error': 'Role not found'}, status=404)

        serializer = RoleSerializer(role, data=request.data, partial=True)
        if serializer.is_valid():
            role = serializer.save()

            # 处理权限分配
            permissions = request.data.get('permissions', [])
            if 'permissions' in request.data:  # 只有当传递了permissions字段时才更新
                from authentication.models import RolePermission
                # 清除现有权限
                RolePermission.objects.filter(role=role).delete()
                # 添加新权限
                for perm_code in permissions:
                    try:
                        permission = Permission.objects.get(code=perm_code)
                        RolePermission.objects.get_or_create(role=role, permission=permission)
                    except Permission.DoesNotExist:
                        pass

            return Response(RoleSerializer(role).data)
        return Response(serializer.errors, status=400)

    def delete(self, request, pk):
        """删除角色"""
        role = self.get_object(pk)
        if not role:
            return Response({'error': 'Role not found'}, status=404)

        # 软删除：设置为不活跃
        role.is_active = False
        role.save()

        return Response({'message': 'Role deleted successfully'})


class RoleCodeExistsView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """检查角色代码是否存在"""
        code = request.GET.get('code', '').strip()
        role_id = request.GET.get('id')  # 编辑时排除自己

        if not code:
            return Response({'exists': False})

        query = Role.objects.filter(code=code, is_active=True)
        if role_id:
            query = query.exclude(id=role_id)

        exists = query.exists()
        return Response({'exists': exists})


class UserListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取用户列表"""
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('pageSize', 20))

        users = User.objects.filter(is_active=True).order_by('id')

        # 分页
        paginator = Paginator(users, page_size)
        page_obj = paginator.get_page(page)

        serializer = UserSerializer(page_obj.object_list, many=True)

        return Response({
            'list': serializer.data,
            'total': paginator.count,
            'page': page,
            'pageSize': page_size,
            'totalPages': paginator.num_pages,
        })


class PermissionListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取权限列表"""
        permissions = Permission.objects.filter(is_active=True).order_by('id')

        # 始终返回数组格式，避免前端forEach错误
        serializer = PermissionSerializer(permissions, many=True)
        return Response(serializer.data)


class PermissionTreeView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取权限树形结构（用于表单选择）"""
        permissions = Permission.objects.filter(is_active=True).order_by('resource', 'action')

        # 按资源分组构建树形结构
        tree_data = []
        resource_groups = {}

        for permission in permissions:
            resource = permission.resource or '其他'

            if resource not in resource_groups:
                resource_groups[resource] = {
                    'key': f'resource_{resource}',
                    'title': resource,
                    'label': resource,
                    'value': f'resource_{resource}',
                    'children': [],
                    'selectable': False,  # 资源组不可选择
                }

            # 添加权限项
            permission_item = {
                'key': permission.code,
                'title': f"{permission.name} ({permission.code})",
                'label': f"{permission.name} ({permission.code})",
                'value': permission.code,
                'id': permission.id,
                'name': permission.name,
                'code': permission.code,
                'description': permission.description,
            }
            resource_groups[resource]['children'].append(permission_item)

        # 转换为列表
        tree_data = list(resource_groups.values())

        return Response(tree_data)


class PermissionAllView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取所有权限（简单列表格式，用于表单）"""
        permissions = Permission.objects.filter(is_active=True).order_by('resource', 'action')

        # 返回简单的权限列表
        permission_list = []
        for permission in permissions:
            permission_list.append({
                'id': permission.id,
                'name': permission.name,
                'code': permission.code,
                'label': f"{permission.name} ({permission.code})",
                'value': permission.code,
                'description': permission.description,
                'resource': permission.resource,
                'action': permission.action,
            })

        return Response(permission_list)


class MenuManagementListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取菜单列表（用于系统管理）"""
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('pageSize', 20))

        menus = Menu.objects.all().order_by('order', 'id')

        # 分页
        paginator = Paginator(menus, page_size)
        page_obj = paginator.get_page(page)

        serializer = MenuSerializer(page_obj.object_list, many=True)

        return Response({
            'list': serializer.data,
            'total': paginator.count,
            'page': page,
            'pageSize': page_size,
            'totalPages': paginator.num_pages,
        })


class MenuNameExistsView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """检查菜单名称是否存在"""
        name = request.GET.get('name', '').strip()
        menu_id = request.GET.get('id')  # 编辑时排除自己

        if not name:
            return Response({'exists': False})

        query = Menu.objects.filter(name=name)
        if menu_id:
            query = query.exclude(id=menu_id)

        exists = query.exists()
        return Response({'exists': exists})


class MenuPathExistsView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """检查菜单路径是否存在"""
        path = request.GET.get('path', '').strip()
        menu_id = request.GET.get('id')  # 编辑时排除自己

        if not path:
            return Response({'exists': False})

        query = Menu.objects.filter(path=path)
        if menu_id:
            query = query.exclude(id=menu_id)

        exists = query.exists()
        return Response({'exists': exists})
