from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth import get_user_model
from django.core.paginator import Paginator
from authentication.models import Role
from authentication.serializers import RoleSerializer, UserSerializer
from .models import Department
from .serializers import DepartmentSerializer

User = get_user_model()


class DepartmentListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取部门列表"""
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('pageSize', 20))

        departments = Department.objects.filter(status=1).order_by('order', 'id')

        # 分页
        paginator = Paginator(departments, page_size)
        page_obj = paginator.get_page(page)

        serializer = DepartmentSerializer(page_obj.object_list, many=True)

        return Response({
            'list': serializer.data,
            'total': paginator.count,
            'page': page,
            'pageSize': page_size,
            'totalPages': paginator.num_pages,
        })


class RoleListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取角色列表"""
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('pageSize', 20))

        roles = Role.objects.filter(is_active=True).order_by('id')

        # 分页
        paginator = Paginator(roles, page_size)
        page_obj = paginator.get_page(page)

        serializer = RoleSerializer(page_obj.object_list, many=True)

        return Response({
            'list': serializer.data,
            'total': paginator.count,
            'page': page,
            'pageSize': page_size,
            'totalPages': paginator.num_pages,
        })


class UserListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取用户列表"""
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('pageSize', 20))

        users = User.objects.filter(is_active=True).order_by('id')

        # 分页
        paginator = Paginator(users, page_size)
        page_obj = paginator.get_page(page)

        serializer = UserSerializer(page_obj.object_list, many=True)

        return Response({
            'list': serializer.data,
            'total': paginator.count,
            'page': page,
            'pageSize': page_size,
            'totalPages': paginator.num_pages,
        })
