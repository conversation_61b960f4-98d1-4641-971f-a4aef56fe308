from rest_framework import serializers
from .models import Department, Position, UserDepartment


class DepartmentSerializer(serializers.ModelSerializer):
    """部门序列化器"""
    children = serializers.SerializerMethodField()
    manager_name = serializers.CharField(source='manager.real_name', read_only=True)
    user_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Department
        fields = ['id', 'name', 'code', 'description', 'parent', 'manager',
                 'manager_name', 'order', 'status', 'children', 'user_count',
                 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_children(self, obj):
        """获取子部门"""
        if self.context.get('include_children', True):
            children = obj.get_children()
            context = self.context.copy()
            context['include_children'] = False
            return DepartmentSerializer(children, many=True, context=context).data
        return []
    
    def get_user_count(self, obj):
        """获取部门用户数量"""
        return UserDepartment.objects.filter(department=obj).count()


class DepartmentTreeSerializer(serializers.ModelSerializer):
    """部门树形序列化器"""
    children = serializers.SerializerMethodField()
    
    class Meta:
        model = Department
        fields = ['id', 'name', 'code', 'status', 'parent', 'order', 'children']
    
    def get_children(self, obj):
        """递归获取子部门"""
        children = obj.get_children()
        return DepartmentTreeSerializer(children, many=True).data


class PositionSerializer(serializers.ModelSerializer):
    """职位序列化器"""
    department_name = serializers.CharField(source='department.name', read_only=True)
    user_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Position
        fields = ['id', 'name', 'code', 'description', 'department', 'department_name',
                 'level', 'status', 'user_count', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_user_count(self, obj):
        """获取职位用户数量"""
        return UserDepartment.objects.filter(position=obj).count()


class UserDepartmentSerializer(serializers.ModelSerializer):
    """用户部门关联序列化器"""
    user_name = serializers.CharField(source='user.real_name', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    position_name = serializers.CharField(source='position.name', read_only=True)
    
    class Meta:
        model = UserDepartment
        fields = ['id', 'user', 'user_name', 'department', 'department_name',
                 'position', 'position_name', 'is_primary', 'created_at']
        read_only_fields = ['id', 'created_at']


class DepartmentCreateUpdateSerializer(serializers.ModelSerializer):
    """部门创建/更新序列化器"""
    
    class Meta:
        model = Department
        fields = ['name', 'code', 'description', 'parent', 'manager', 'order', 'status']
    
    def validate_parent(self, value):
        """验证父部门"""
        if value and self.instance:
            # 检查是否会形成循环引用
            if value.id == self.instance.id:
                raise serializers.ValidationError('不能将自己设为父部门')
            
            # 检查是否是自己的子部门
            all_children = self.instance.get_all_children()
            if value in all_children:
                raise serializers.ValidationError('不能将子部门设为父部门')
        
        return value
    
    def validate_code(self, value):
        """验证部门代码唯一性"""
        if self.instance:
            # 更新时排除自己
            if Department.objects.exclude(id=self.instance.id).filter(code=value).exists():
                raise serializers.ValidationError('部门代码已存在')
        else:
            # 创建时检查
            if Department.objects.filter(code=value).exists():
                raise serializers.ValidationError('部门代码已存在')
        return value
