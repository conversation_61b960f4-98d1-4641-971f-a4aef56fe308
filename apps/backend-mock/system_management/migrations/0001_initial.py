# Generated by Django 5.2.1 on 2025-06-04 05:09

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='部门名称')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='部门代码')),
                ('description', models.TextField(blank=True, verbose_name='部门描述')),
                ('order', models.IntegerField(default=0, verbose_name='排序')),
                ('status', models.IntegerField(choices=[(0, '禁用'), (1, '启用')], default=1, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='部门负责人')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='system_management.department', verbose_name='上级部门')),
            ],
            options={
                'verbose_name': '部门',
                'verbose_name_plural': '部门',
                'db_table': 'system_department',
                'ordering': ['order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='Position',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='职位名称')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='职位代码')),
                ('description', models.TextField(blank=True, verbose_name='职位描述')),
                ('level', models.IntegerField(default=1, verbose_name='职位级别')),
                ('status', models.IntegerField(choices=[(0, '禁用'), (1, '启用')], default=1, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='system_management.department', verbose_name='所属部门')),
            ],
            options={
                'verbose_name': '职位',
                'verbose_name_plural': '职位',
                'db_table': 'system_position',
                'ordering': ['level', 'id'],
            },
        ),
        migrations.CreateModel(
            name='UserDepartment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_primary', models.BooleanField(default=True, verbose_name='是否主部门')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='system_management.department', verbose_name='部门')),
                ('position', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='system_management.position', verbose_name='职位')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户部门',
                'verbose_name_plural': '用户部门',
                'db_table': 'system_user_department',
                'unique_together': {('user', 'department')},
            },
        ),
    ]
